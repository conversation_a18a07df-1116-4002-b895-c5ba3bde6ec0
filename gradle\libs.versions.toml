[versions]
androidGradlePlugin = "8.13.0"
annotation = "1.9.1"
json = "20250517"
kotlin = "2.2.10"
compose = "1.9.2"
lifecycle = "2.9.4"
material3 = "1.4.0"
appcompat = "1.7.1"
media3 = "1.7.1"
room = "2.7.2"
hilt = "2.57.1"
ktor = "3.3.0"
ksp = "2.2.10-2.0.2"
jsoup = "1.21.2"
multidex = "2.0.1"
coil = "3.3.0"
ucrop = "2.2.11"
guava = "33.5.0-android"
coroutinesGuava = "1.10.2"
concurrentFutures = "1.3.0"
activity = "1.11.0"
navigation = "2.9.5"
hiltNavigation = "1.3.0"
datastore = "1.1.7"
composeReorderable = "3.0.0"
squigglyslider = "1.0.0"
shimmer = "1.3.3"
palette = "1.0.0"
apacheLang3 = "3.19.0"
brotli = "0.1.2"
desugaring = "2.1.5"
junit = "4.13.2"
timber = "5.0.1"
materialKolor = "3.0.1"
kuromojiIpadic = "0.9.0"
extractor = "06b7ae2"

[libraries]
annotation = { module = "androidx.annotation:annotation", version.ref = "annotation" }
guava = { module = "com.google.guava:guava", version.ref = "guava" }
coroutines-guava = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-guava", version.ref = "coroutinesGuava" }
concurrent-futures = { module = "androidx.concurrent:concurrent-futures-ktx", version.ref = "concurrentFutures" }

gradle = { module = "com.android.tools.build:gradle", version.ref = "androidGradlePlugin" }
activity = { module = "androidx.activity:activity-compose", version.ref = "activity" }
navigation = { module = "androidx.navigation:navigation-compose", version.ref = "navigation" }
hilt-navigation = { module = "androidx.hilt:hilt-navigation-compose", version.ref = "hiltNavigation" }
datastore = { module = "androidx.datastore:datastore-preferences", version.ref = "datastore" }

compose-runtime = { module = "androidx.compose.runtime:runtime", version.ref = "compose" }
compose-foundation = { module = "androidx.compose.foundation:foundation", version.ref = "compose" }
compose-ui = { module = "androidx.compose.ui:ui", version.ref = "compose" }
compose-ui-util = { module = "androidx.compose.ui:ui-util", version.ref = "compose" }
compose-ui-tooling = { module = "androidx.compose.ui:ui-tooling", version.ref = "compose" }
compose-animation = { module = "androidx.compose.animation:animation-graphics", version.ref = "compose" }
compose-reorderable = { module = "sh.calvin.reorderable:reorderable", version.ref = "composeReorderable" }

squigglyslider = { module = "me.saket.squigglyslider:squigglyslider", version.ref = "squigglyslider" }

viewmodel = { module = "androidx.lifecycle:lifecycle-viewmodel-ktx", version.ref = "lifecycle" }
viewmodel-compose = { module = "androidx.lifecycle:lifecycle-viewmodel-compose", version.ref = "lifecycle" }

material3 = { module = "androidx.compose.material3:material3", version.ref = "material3" }

materialKolor = { module = "com.materialkolor:material-kolor", version.ref = "materialKolor" }

appcompat = { module = "androidx.appcompat:appcompat", version.ref = "appcompat" }

coil = { module = "io.coil-kt.coil3:coil-compose", version.ref = "coil" }
coil-network-okhttp = { module = "io.coil-kt.coil3:coil-network-okhttp", version.ref = "coil" }
ucrop = { module = "com.github.yalantis:ucrop", version.ref = "ucrop" }

shimmer = { module = "com.valentinilk.shimmer:compose-shimmer", version.ref = "shimmer" }

palette = { module = "androidx.palette:palette-ktx", version.ref = "palette" }

media3 = { module = "androidx.media3:media3-exoplayer", version.ref = "media3" }
media3-okhttp = { module = "androidx.media3:media3-datasource-okhttp", version.ref = "media3" }
media3-session = { module = "androidx.media3:media3-session", version.ref = "media3" }

room-runtime = { module = "androidx.room:room-runtime", version.ref = "room" }
room-compiler = { module = "androidx.room:room-compiler", version.ref = "room" }
room-ktx = { module = "androidx.room:room-ktx", version.ref = "room" }

apache-lang3 = { module = "org.apache.commons:commons-lang3", version.ref = "apacheLang3" }

hilt = { module = "com.google.dagger:hilt-android", version.ref = "hilt" }
hilt-compiler = { module = "com.google.dagger:hilt-android-compiler", version.ref = "hilt" }

ktor-client-core = { module = "io.ktor:ktor-client-core", version.ref = "ktor" }
ktor-client-cio = { module = "io.ktor:ktor-client-cio", version.ref = "ktor" }
ktor-client-okhttp = { module = "io.ktor:ktor-client-okhttp", version.ref = "ktor" }
ktor-client-content-negotiation = { module = "io.ktor:ktor-client-content-negotiation", version.ref = "ktor" }
ktor-client-encoding = { module = "io.ktor:ktor-client-encoding", version.ref = "ktor" }
ktor-serialization-json = { module = "io.ktor:ktor-serialization-kotlinx-json", version.ref = "ktor" }

jsoup = { module = "org.jsoup:jsoup", version.ref = "jsoup" }

json = { module = "org.json:json", version.ref = "json" }

brotli = { module = "org.brotli:dec", version.ref = "brotli" }

desugaring = { module = "com.android.tools:desugar_jdk_libs", version.ref = "desugaring" }

junit = { module = "junit:junit", version.ref = "junit" }

timber = { module = "com.jakewharton.timber:timber", version.ref = "timber" }

multidex = { module = "androidx.multidex:multidex", version.ref = "multidex" }

#newpipe-extractor = { module = "com.github.libre-tube:NewPipeExtractor", version = "70abbdb" }
#newpipe-extractor = { module = "com.github.TeamNewPipe:NewPipeExtractor", version = "dev-SNAPSHOT" }
extractor = { module = "com.github.mostafaalagamy:MetrolistExtractor", version.ref = "extractor" }

kuromoji-ipadic = { module = "com.atilika.kuromoji:kuromoji-ipadic", version.ref = "kuromojiIpadic" }

[plugins]
compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
hilt = { id = "com.google.dagger.hilt.android", version.ref = "hilt" }
kotlin-ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
