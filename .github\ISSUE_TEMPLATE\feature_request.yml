name: Feature request
description: Suggest an idea for Metrolist
labels: [enhancement]
body:
  - type: checkboxes
    id: checklist
    attributes:
      label: Checklist
      description: You should ensure the completion of the task before proceeding to check it off the checklist. Neglecting to do so may impede the efficiency of the issue resolution process. The developer has the right to delete the issue directly if you check the list blindly.
      options:
        - label: I've checked that there is no other issue about this feature request.
          required: true
        - label: This issue contains only one feature request.
          required: true
        - label: The title of this issue accurately describes the feature request.
          required: true

  - type: textarea
    id: feature-description
    attributes:
      label: Feature description
      description: What feature you want the app to have? Provide detailed description about what it should look like or where it should be added.
    validations:
      required: true

  - type: textarea
    id: why-is-the-feature-requested
    attributes:
      label: Why do you want this feature?
      description: Describe the problem or limitation that motivates you to want this feature to be added.
    validations:
      required: true

  - type: textarea
    id: additional-information
    attributes:
      label: Additional information
      description: Add any other context or screenshots about the feature request here.
      placeholder: |
        Additional details and attachments.
