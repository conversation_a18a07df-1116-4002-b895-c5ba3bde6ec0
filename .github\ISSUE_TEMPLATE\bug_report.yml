name: Bug report
description: Create a bug report to help us improve
labels: [ bug ]
body:
  - type: checkboxes
    id: checklist
    attributes:
      label: Checklist
      description: You should ensure the completion of the task before proceeding to check it off the checklist. Neglecting to do so may impede the efficiency of the issue resolution process. The developer has the right to delete the issue directly if you check the list blindly.
      options:
        - label: I am able to reproduce the bug with the [latest debug version](https://github.com/mostafaalagamy/Metrolist/actions).
          required: true
        - label: I've checked that there is no open or closed issue about this bug.
          required: true
        - label: This issue contains only one bug.
          required: true
        - label: The title of this issue accurately describes the bug.
          required: true

  - type: textarea
    id: reproduce-steps
    attributes:
      label: Steps to reproduce the bug
      description: What did you do for the bug to show up?
      placeholder: |
        Example:
          1. Go to '...'
          2. Click on '....'
          3. Scroll down to '....'
          4. See error
    validations:
      required: true

  - type: textarea
    id: expected-behavior
    attributes:
      label: Expected behavior
      placeholder: |
        Example:
          "This should happen..."
    validations:
      required: true

  - type: textarea
    id: actual-behavior
    attributes:
      label: Actual behavior
      placeholder: |
        Example:
          "This happened instead..."
    validations:
      required: true

  - type: textarea
    id: sreen-media
    attributes:
      label: Screenshots/Screen recordings
      description: |
        A picture or video helps us understand the bug more.

        You can upload them directly in the text box.

  - type: textarea
    id: logs
    attributes:
      label: Logs
      description: |
        Please use `adb logcat` or other ways to provide logs. This field is strongly recommended to be filled. Without this information, it's likely that the developer is unable to take any meaningful action or provide further assistance.
    validations:
      required: true

  - type: input
    id: app-version
    attributes:
      label: Metrolist version
      description: |
        You can find your Metrolist version in **Settings**.
      placeholder: |
        Example: "0.5.3"
    validations:
      required: true

  - type: input
    id: android-version
    attributes:
      label: Android version
      description: |
        You can find this somewhere in your Android settings.
      placeholder: |
        Example: "Android 12"
    validations:
      required: true

  - type: textarea
    id: additional-information
    attributes:
      label: Additional information
      placeholder: |
        Additional details and attachments.
