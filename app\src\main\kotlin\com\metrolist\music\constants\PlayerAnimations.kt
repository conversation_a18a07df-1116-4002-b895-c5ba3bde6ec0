package com.metrolist.music.constants

import androidx.compose.animation.core.CubicBezierEasing
import androidx.compose.animation.core.Easing
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.LinearOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.AnimationSpec
import androidx.compose.ui.unit.Dp

/**
 * YouTube Music-style animation configurations
 * These easing curves and durations match the smooth transitions seen in YouTube Music
 */
object PlayerAnimations {
    
    // YouTube Music-style easing curves
    val YouTubeMusicEasing: Easing = CubicBezierEasing(0.4f, 0.0f, 0.2f, 1.0f)
    val YouTubeMusicFastOutSlowIn: Easing = CubicBezierEasing(0.4f, 0.0f, 0.2f, 1.0f)
    val YouTubeMusicLinearOutSlowIn: Easing = CubicBezierEasing(0.0f, 0.0f, 0.2f, 1.0f)
    val YouTubeMusicFastOutLinearIn: Easing = CubicBezierEasing(0.4f, 0.0f, 1.0f, 1.0f)
    
    // Animation durations (in milliseconds)
    const val SHARED_ELEMENT_DURATION = 300
    const val BACKGROUND_FADE_DURATION = 250
    const val CONTENT_FADE_DURATION = 200
    const val THUMBNAIL_SCALE_DURATION = 350
    const val MINI_PLAYER_FADE_DURATION = 150
    
    // Animation specs for different elements
    val SharedElementAnimationSpec: AnimationSpec<Float> = tween(
        durationMillis = SHARED_ELEMENT_DURATION,
        easing = YouTubeMusicFastOutSlowIn
    )
    
    val BackgroundFadeAnimationSpec: AnimationSpec<Float> = tween(
        durationMillis = BACKGROUND_FADE_DURATION,
        easing = YouTubeMusicLinearOutSlowIn
    )
    
    val ContentFadeAnimationSpec: AnimationSpec<Float> = tween(
        durationMillis = CONTENT_FADE_DURATION,
        easing = FastOutSlowInEasing
    )
    
    val ThumbnailScaleAnimationSpec: AnimationSpec<Float> = tween(
        durationMillis = THUMBNAIL_SCALE_DURATION,
        easing = YouTubeMusicFastOutSlowIn
    )
    
    val MiniPlayerFadeAnimationSpec: AnimationSpec<Float> = tween(
        durationMillis = MINI_PLAYER_FADE_DURATION,
        easing = LinearOutSlowInEasing
    )
    
    // Dp animation specs
    val BottomSheetAnimationSpec: AnimationSpec<Dp> = tween(
        durationMillis = SHARED_ELEMENT_DURATION,
        easing = YouTubeMusicFastOutSlowIn
    )
    
    // Scale factors for shared element transitions
    const val MINI_PLAYER_THUMBNAIL_SCALE = 1f
    const val FULL_PLAYER_THUMBNAIL_SCALE = 9f
    const val THUMBNAIL_TRANSLATION_FACTOR = 200f
    
    // Alpha values for smooth fading
    const val MINI_PLAYER_VISIBLE_ALPHA = 1f
    const val MINI_PLAYER_HIDDEN_ALPHA = 0f
    const val FULL_PLAYER_VISIBLE_ALPHA = 1f
    const val FULL_PLAYER_HIDDEN_ALPHA = 0f
    
    // Progress thresholds for different animation phases
    const val FADE_START_THRESHOLD = 0.1f
    const val FADE_END_THRESHOLD = 0.9f
    const val SCALE_START_THRESHOLD = 0.0f
    const val SCALE_END_THRESHOLD = 1.0f
    
    /**
     * Calculate smooth interpolation for shared element transitions
     */
    fun calculateSharedElementProgress(
        progress: Float,
        startThreshold: Float = SCALE_START_THRESHOLD,
        endThreshold: Float = SCALE_END_THRESHOLD
    ): Float {
        return ((progress - startThreshold) / (endThreshold - startThreshold))
            .coerceIn(0f, 1f)
    }
    
    /**
     * Calculate fade progress with custom thresholds
     */
    fun calculateFadeProgress(
        progress: Float,
        startThreshold: Float = FADE_START_THRESHOLD,
        endThreshold: Float = FADE_END_THRESHOLD
    ): Float {
        return ((progress - startThreshold) / (endThreshold - startThreshold))
            .coerceIn(0f, 1f)
    }
    
    /**
     * Calculate scale value for thumbnail transition
     */
    fun calculateThumbnailScale(
        progress: Float,
        minScale: Float = MINI_PLAYER_THUMBNAIL_SCALE,
        maxScale: Float = FULL_PLAYER_THUMBNAIL_SCALE
    ): Float {
        val adjustedProgress = calculateSharedElementProgress(progress)
        return minScale + (maxScale - minScale) * adjustedProgress
    }
    
    /**
     * Calculate translation Y for thumbnail movement
     */
    fun calculateThumbnailTranslationY(
        progress: Float,
        maxTranslation: Float = THUMBNAIL_TRANSLATION_FACTOR
    ): Float {
        val adjustedProgress = calculateSharedElementProgress(progress)
        return -adjustedProgress * maxTranslation
    }
}
