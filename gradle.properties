## For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
#
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx1024m -XX:MaxPermSize=256m
# org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
#
# When configured, Grad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
#Sat Nov 19 15:59:34 CST 2022

org.gradle.jvmargs=-Xmx4096M -Dkotlin.daemon.jvm.options\="-Xmx4096M" -XX:+UseParallelGC
android.useAndroidX=true
android.enableJetifier=false
org.gradle.unsafe.configuration-cache=true
android.nonTransitiveRClass=false

# Jetifier is disabled - no need for ignorelist

# Performance improvements
org.gradle.parallel=true
org.gradle.daemon=true
org.gradle.configureondemand=false

# Suppress deprecated warnings
android.suppressUnsupportedOptionWarnings=android.suppressUnsupportedOptionWarnings,android.nonFinalResIds

# Increase timeouts for JitPack downloads (fixes timeout issues with NewPipeExtractor)
systemProp.org.gradle.internal.http.connectionTimeout=180000
systemProp.org.gradle.internal.http.socketTimeout=180000

# Disable caching for SNAPSHOT dependencies to avoid timeout issues
org.gradle.caching=false
