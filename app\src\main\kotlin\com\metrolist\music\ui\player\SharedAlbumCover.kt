package com.metrolist.music.ui.player

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import coil3.compose.AsyncImage
import com.metrolist.music.constants.PlayerAnimations
import com.metrolist.music.constants.PlayerHorizontalPadding
import com.metrolist.music.constants.ThumbnailCornerRadius
import com.metrolist.music.models.MediaMetadata
import com.metrolist.music.ui.component.BottomSheetState
import kotlin.math.roundToInt

/**
 * Shared Album Cover that transitions between Mini Player and Full Player positions
 * This creates the YouTube Music-style smooth transition effect
 */
@Composable
fun SharedAlbumCover(
    mediaMetadata: MediaMetadata?,
    playerState: BottomSheetState,
    miniPlayerHeight: Dp,
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current
    val configuration = LocalConfiguration.current
    val screenHeight = configuration.screenHeightDp.dp
    val screenWidth = configuration.screenWidthDp.dp
    
    // Calculate positions more precisely
    val miniPlayerY = with(density) {
        // Mini player is at the bottom, account for system bars and padding
        (screenHeight - miniPlayerHeight - 12.dp).toPx() // 12dp is the bottom padding
    }
    val fullPlayerY = with(density) {
        // Full player thumbnail should be in the upper portion
        (screenHeight * 0.25f).toPx()
    }

    val miniPlayerX = with(density) {
        // Mini player thumbnail position (left side with padding)
        (12.dp + 6.dp + 24.dp).toPx() // horizontal padding + internal padding + half thumbnail
    }
    val fullPlayerX = with(density) {
        // Center of screen for full player
        (screenWidth / 2f).toPx()
    }
    
    // Animation values based on player progress
    val progress = playerState.progress
    
    // Position animation
    val animatedX by animateFloatAsState(
        targetValue = miniPlayerX + (fullPlayerX - miniPlayerX) * progress,
        animationSpec = PlayerAnimations.SharedElementAnimationSpec,
        label = "album_cover_x"
    )
    
    val animatedY by animateFloatAsState(
        targetValue = miniPlayerY + (fullPlayerY - miniPlayerY) * progress,
        animationSpec = PlayerAnimations.SharedElementAnimationSpec,
        label = "album_cover_y"
    )
    
    // Size animation - match the actual sizes used in the app
    val miniSize = 48.dp // Size used in LegacyMiniMediaInfo
    val fullSize = 300.dp // Size for full player
    
    val animatedSize by animateFloatAsState(
        targetValue = with(density) {
            (miniSize + (fullSize - miniSize) * progress).toPx()
        },
        animationSpec = PlayerAnimations.ThumbnailScaleAnimationSpec,
        label = "album_cover_size"
    )
    
    // Corner radius animation
    val animatedCornerRadius by animateFloatAsState(
        targetValue = if (progress < 0.5f) {
            // Start as circle, gradually become rounded rectangle
            with(density) { (miniSize / 2f * (1f - progress * 2f)).toPx() }
        } else {
            // Become rounded rectangle
            with(density) { (ThumbnailCornerRadius * 2).toPx() }
        },
        animationSpec = PlayerAnimations.SharedElementAnimationSpec,
        label = "album_cover_corner_radius"
    )
    
    // Visibility - only show during transition and when collapsed
    val shouldShow = !playerState.isExpanded || progress > 0f
    
    if (shouldShow && mediaMetadata != null) {
        Box(
            modifier = modifier
                .zIndex(100f) // Ensure it's above everything else
                .offset {
                    IntOffset(
                        x = animatedX.roundToInt(),
                        y = animatedY.roundToInt()
                    )
                }
        ) {
            AsyncImage(
                model = mediaMetadata.thumbnailUrl,
                contentDescription = null,
                contentScale = ContentScale.Crop,
                modifier = Modifier
                    .size(with(density) { animatedSize.toDp() })
                    .clip(
                        RoundedCornerShape(
                            with(density) { animatedCornerRadius.toDp() }
                        )
                    )
                    .graphicsLayer {
                        // Ensure smooth rendering
                        compositingStrategy = androidx.compose.ui.graphics.CompositingStrategy.Offscreen
                    }
            )
        }
    }
}

/**
 * Mini Player without the album cover (since it's rendered separately)
 */
@Composable
fun MiniPlayerWithoutCover(
    position: Long,
    duration: Long,
    pureBlack: Boolean,
    modifier: Modifier = Modifier
) {
    // Use the existing MiniPlayer but with a transparent album cover area
    MiniPlayer(
        position = position,
        duration = duration,
        pureBlack = pureBlack,
        playerProgress = 0f,
        hideAlbumCover = true, // New parameter to hide album cover
        modifier = modifier
    )
}

/**
 * Thumbnail component without shared element (for full player)
 */
@Composable
fun ThumbnailWithoutSharedElement(
    sliderPositionProvider: () -> Long?,
    modifier: Modifier = Modifier,
    isPlayerExpanded: Boolean = true,
    playerProgress: Float = 1f
) {
    // This would be the Thumbnail content without the actual image
    // (since the image is rendered separately as shared element)
    Box(modifier = modifier) {
        // TODO: Implement thumbnail placeholder or background
        // The actual album cover image is rendered by SharedAlbumCover
        // This could show a placeholder, loading state, or background effects
    }
}
