{"formatVersion": 1, "database": {"version": 3, "identityHash": "b0a90e3281fad7803ea9fadbc6aac04f", "entities": [{"tableName": "song", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `title` TEXT NOT NULL, `duration` INTEGER NOT NULL, `thumbnailUrl` TEXT, `albumId` TEXT, `albumName` TEXT, `liked` INTEGER NOT NULL, `totalPlayTime` INTEGER NOT NULL, `isTrash` INTEGER NOT NULL, `download_state` INTEGER NOT NULL, `create_date` INTEGER NOT NULL, `modify_date` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "duration", "columnName": "duration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "thumbnailUrl", "columnName": "thumbnailUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "albumId", "columnName": "albumId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "albumName", "columnName": "albumName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "liked", "columnName": "liked", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalPlayTime", "columnName": "totalPlayTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isTrash", "columnName": "isTrash", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadState", "columnName": "download_state", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createDate", "columnName": "create_date", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "modifyDate", "columnName": "modify_date", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "artist", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `thumbnailUrl` TEXT, `bannerUrl` TEXT, `description` TEXT, `createDate` INTEGER NOT NULL, `lastUpdateTime` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "thumbnailUrl", "columnName": "thumbnailUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bannerUrl", "columnName": "bannerUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createDate", "columnName": "createDate", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastUpdateTime", "columnName": "lastUpdateTime", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "album", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `title` TEXT NOT NULL, `year` INTEGER, `thumbnailUrl` TEXT, `songCount` INTEGER NOT NULL, `duration` INTEGER NOT NULL, `createDate` INTEGER NOT NULL, `lastUpdateTime` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "year", "columnName": "year", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "thumbnailUrl", "columnName": "thumbnailUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "songCount", "columnName": "songCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "duration", "columnName": "duration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createDate", "columnName": "createDate", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastUpdateTime", "columnName": "lastUpdateTime", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "playlist", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `author` TEXT, `authorId` TEXT, `year` INTEGER, `thumbnailUrl` TEXT, `createDate` INTEGER NOT NULL, `lastUpdateTime` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "author", "columnName": "author", "affinity": "TEXT", "notNull": false}, {"fieldPath": "authorId", "columnName": "authorId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "year", "columnName": "year", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "thumbnailUrl", "columnName": "thumbnailUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createDate", "columnName": "createDate", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastUpdateTime", "columnName": "lastUpdateTime", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "song_artist_map", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`songId` TEXT NOT NULL, `artistId` TEXT NOT NULL, `position` INTEGER NOT NULL, PRIMARY KEY(`songId`, `artistId`), FOREI<PERSON><PERSON>EY(`songId`) REFERENCES `song`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREI<PERSON><PERSON> KEY(`artistId`) REFERENCES `artist`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "songId", "columnName": "songId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "artistId", "columnName": "artistId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "position", "columnName": "position", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["songId", "artistId"], "autoGenerate": false}, "indices": [{"name": "index_song_artist_map_songId", "unique": false, "columnNames": ["songId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_song_artist_map_songId` ON `${TABLE_NAME}` (`songId`)"}, {"name": "index_song_artist_map_artistId", "unique": false, "columnNames": ["artistId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_song_artist_map_artistId` ON `${TABLE_NAME}` (`artistId`)"}], "foreignKeys": [{"table": "song", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["songId"], "referencedColumns": ["id"]}, {"table": "artist", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["artistId"], "referencedColumns": ["id"]}]}, {"tableName": "song_album_map", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`songId` TEXT NOT NULL, `albumId` TEXT NOT NULL, `index` INTEGER, PRIMARY KEY(`songId`, `albumId`), FOREI<PERSON><PERSON> KEY(`songId`) REFERENCES `song`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREI<PERSON>N KEY(`albumId`) REFERENCES `album`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "songId", "columnName": "songId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "albumId", "columnName": "albumId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "index", "columnName": "index", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"columnNames": ["songId", "albumId"], "autoGenerate": false}, "indices": [{"name": "index_song_album_map_songId", "unique": false, "columnNames": ["songId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_song_album_map_songId` ON `${TABLE_NAME}` (`songId`)"}, {"name": "index_song_album_map_albumId", "unique": false, "columnNames": ["albumId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_song_album_map_albumId` ON `${TABLE_NAME}` (`albumId`)"}], "foreignKeys": [{"table": "song", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["songId"], "referencedColumns": ["id"]}, {"table": "album", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["albumId"], "referencedColumns": ["id"]}]}, {"tableName": "album_artist_map", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`albumId` TEXT NOT NULL, `artistId` TEXT NOT NULL, `order` INTEGER NOT NULL, PRIMARY KEY(`albumId`, `artistId`), FOREI<PERSON><PERSON>EY(`albumId`) REFERENCES `album`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREI<PERSON><PERSON>EY(`artistId`) REFERENCES `artist`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "albumId", "columnName": "albumId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "artistId", "columnName": "artistId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "order", "columnName": "order", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["albumId", "artistId"], "autoGenerate": false}, "indices": [{"name": "index_album_artist_map_albumId", "unique": false, "columnNames": ["albumId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_album_artist_map_albumId` ON `${TABLE_NAME}` (`albumId`)"}, {"name": "index_album_artist_map_artistId", "unique": false, "columnNames": ["artistId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_album_artist_map_artistId` ON `${TABLE_NAME}` (`artistId`)"}], "foreignKeys": [{"table": "album", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["albumId"], "referencedColumns": ["id"]}, {"table": "artist", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["artistId"], "referencedColumns": ["id"]}]}, {"tableName": "playlist_song_map", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `playlistId` TEXT NOT NULL, `songId` TEXT NOT NULL, `position` INTEGER NOT NULL, FOREIGN KEY(`playlistId`) REFERENCES `playlist`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREIGN KEY(`songId`) REFERENCES `song`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "playlistId", "columnName": "playlistId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "songId", "columnName": "songId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "position", "columnName": "position", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [{"name": "index_playlist_song_map_playlistId", "unique": false, "columnNames": ["playlistId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_playlist_song_map_playlistId` ON `${TABLE_NAME}` (`playlistId`)"}, {"name": "index_playlist_song_map_songId", "unique": false, "columnNames": ["songId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_playlist_song_map_songId` ON `${TABLE_NAME}` (`songId`)"}], "foreignKeys": [{"table": "playlist", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["playlistId"], "referencedColumns": ["id"]}, {"table": "song", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["songId"], "referencedColumns": ["id"]}]}, {"tableName": "download", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `songId` TEXT NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "songId", "columnName": "songId", "affinity": "TEXT", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "search_history", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `query` TEXT NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "query", "columnName": "query", "affinity": "TEXT", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [{"name": "index_search_history_query", "unique": true, "columnNames": ["query"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_search_history_query` ON `${TABLE_NAME}` (`query`)"}], "foreignKeys": []}, {"tableName": "format", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `itag` INTEGER NOT NULL, `mimeType` TEXT NOT NULL, `codecs` TEXT NOT NULL, `bitrate` INTEGER NOT NULL, `sampleRate` INTEGER, `contentLength` INTEGER NOT NULL, `loudnessDb` REAL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "itag", "columnName": "itag", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "mimeType", "columnName": "mimeType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "codecs", "columnName": "codecs", "affinity": "TEXT", "notNull": true}, {"fieldPath": "bitrate", "columnName": "bitrate", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sampleRate", "columnName": "sampleRate", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "contentLength", "columnName": "contentLength", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "loudnessDb", "columnName": "loudnessDb", "affinity": "REAL", "notNull": false}], "primaryKey": {"columnNames": ["id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}], "views": [{"viewName": "sorted_song_artist_map", "createSql": "CREATE VIEW `${VIEW_NAME}` AS SELECT * FROM song_artist_map ORDER BY position"}, {"viewName": "playlist_song_map_preview", "createSql": "CREATE VIEW `${VIEW_NAME}` AS SELECT * FROM playlist_song_map WHERE position <= 3 ORDER BY position"}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'b0a90e3281fad7803ea9fadbc6aac04f')"]}}