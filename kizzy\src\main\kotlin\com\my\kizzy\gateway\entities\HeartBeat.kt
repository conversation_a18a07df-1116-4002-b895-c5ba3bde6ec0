/*
 *
 *  ******************************************************************
 *  *  * Copyright (C) 2022
 *  *  * HeartBeat.kt is part of Kizzy
 *  *  *  and can not be copied and/or distributed without the express
 *  *  * permission of <PERSON><PERSON><PERSON>(Vaibhav)
 *  *  *****************************************************************
 *
 *
 */

package com.my.kizzy.gateway.entities

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Heartbeat(
    @SerialName("heartbeat_interval")
    val heartbeatInterval: Long,
)