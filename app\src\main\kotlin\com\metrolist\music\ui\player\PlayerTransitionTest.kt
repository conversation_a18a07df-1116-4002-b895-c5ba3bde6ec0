package com.metrolist.music.ui.player

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.metrolist.music.constants.PlayerAnimations
import com.metrolist.music.ui.theme.MetrolistTheme

/**
 * Test composable to verify the YouTube Music-style transition animations
 * This helps developers see how the shared element transitions work
 */
@Composable
fun PlayerTransitionTest(
    progress: Float = 0.5f
) {
    Box(modifier = Modifier.fillMaxSize()) {
        // Test the animation calculations
        val thumbnailScale = PlayerAnimations.calculateThumbnailScale(progress)
        val thumbnailTranslationY = PlayerAnimations.calculateThumbnailTranslationY(progress)
        val fadeProgress = PlayerAnimations.calculateFadeProgress(progress)
        
        // Display animation values for debugging
        androidx.compose.material3.Text(
            text = """
                Progress: $progress
                Thumbnail Scale: $thumbnailScale
                Translation Y: $thumbnailTranslationY
                Fade Progress: $fadeProgress
            """.trimIndent(),
            style = MaterialTheme.typography.bodyMedium
        )
    }
}

@Preview(showBackground = true)
@Composable
fun PlayerTransitionTestPreview() {
    MetrolistTheme {
        var progress by remember { mutableFloatStateOf(0.5f) }
        PlayerTransitionTest(progress = progress)
    }
}

/**
 * Animation verification functions
 */
object PlayerTransitionVerification {
    
    /**
     * Verify that animations follow YouTube Music-style curves
     */
    fun verifyAnimationCurves(): Boolean {
        // Test key points in the animation
        val testPoints = listOf(0f, 0.25f, 0.5f, 0.75f, 1f)
        
        for (progress in testPoints) {
            val scale = PlayerAnimations.calculateThumbnailScale(progress)
            val translation = PlayerAnimations.calculateThumbnailTranslationY(progress)
            val fade = PlayerAnimations.calculateFadeProgress(progress)
            
            // Verify bounds
            if (scale < PlayerAnimations.MINI_PLAYER_THUMBNAIL_SCALE || 
                scale > PlayerAnimations.FULL_PLAYER_THUMBNAIL_SCALE) {
                return false
            }
            
            if (translation > 0f || translation < -PlayerAnimations.THUMBNAIL_TRANSLATION_FACTOR) {
                return false
            }
            
            if (fade < 0f || fade > 1f) {
                return false
            }
        }
        
        return true
    }
    
    /**
     * Test smooth transition between states
     */
    fun testSmoothTransition(): Boolean {
        val steps = 100
        var previousScale = PlayerAnimations.MINI_PLAYER_THUMBNAIL_SCALE
        
        for (i in 0..steps) {
            val progress = i.toFloat() / steps
            val currentScale = PlayerAnimations.calculateThumbnailScale(progress)
            
            // Check for smooth progression (no sudden jumps)
            val scaleDiff = kotlin.math.abs(currentScale - previousScale)
            if (scaleDiff > 0.5f) { // Allow reasonable step size
                return false
            }
            
            previousScale = currentScale
        }
        
        return true
    }
}
