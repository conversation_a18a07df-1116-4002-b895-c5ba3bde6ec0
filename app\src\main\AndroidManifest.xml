<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />

    <queries>
        <intent>
            <action android:name="android.media.action.DISPLAY_AUDIO_EFFECT_CONTROL_PANEL" />
        </intent>
    </queries>
    <application
        android:name=".App"
        android:allowBackup="true"
        android:appCategory="audio"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:enableOnBackInvokedCallback="true"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="false"
        android:theme="@style/Theme.Metrolist"
        tools:targetApi="tiramisu">
        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/Theme.Metrolist"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <action android:name="android.intent.action.VIEW" />
                <action android:name="android.intent.action.MUSIC_PLAYER" />

                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.APP_MUSIC" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <!-- Youtube filter -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <action android:name="android.media.action.MEDIA_PLAY_FROM_SEARCH" />
                <action android:name="android.nfc.action.NDEF_DISCOVERED" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:host="youtube.com" />
                <data android:host="m.youtube.com" />
                <data android:host="www.youtube.com" />
                <data android:host="music.youtube.com" />
                <!-- video prefix -->
                <data android:pathPrefix="/v/" />
                <data android:pathPrefix="/embed/" />
                <data android:pathPrefix="/watch" />
                <!-- channel prefix -->
                <data android:pathPrefix="/channel/" />
                <data android:pathPrefix="/user/" />
                <data android:pathPrefix="/c/" />
                <!-- playlist prefix -->
                <data android:pathPrefix="/playlist" />
                <!-- browse prefix -->
                <data android:pathPrefix="/browse/" />
                <!-- search prefix -->
                <data android:pathPrefix="/search" />
            </intent-filter>

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <action android:name="android.media.action.MEDIA_PLAY_FROM_SEARCH" />
                <action android:name="android.nfc.action.NDEF_DISCOVERED" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:host="youtu.be" />
                <data android:pathPrefix="/" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <action android:name="android.media.action.MEDIA_PLAY_FROM_SEARCH" />
                <action android:name="android.nfc.action.NDEF_DISCOVERED" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="vnd.youtube" />
                <data android:scheme="vnd.youtube.launch" />
            </intent-filter>

            <!-- Share filter -->
            <intent-filter>
                <action android:name="android.intent.action.SEND" />

                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="text/plain" />
            </intent-filter>

            <meta-data
                android:name="android.app.shortcuts"
                android:resource="@xml/shortcuts" />
        </activity>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.FileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>

        <activity
            android:name="com.yalantis.ucrop.UCropActivity"
            android:screenOrientation="fullSensor"
            android:exported="false"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />

        <service
            android:name=".playback.MusicService"
            android:exported="true"
            android:foregroundServiceType="mediaPlayback"
            tools:ignore="ExportedService">
            <intent-filter>
                <action android:name="androidx.media3.session.MediaSessionService" />
                <action android:name="androidx.media3.session.MediaLibraryService" />
                <action android:name="android.media.browse.MediaBrowserService" />
            </intent-filter>
        </service>

        <service
            android:name=".playback.ExoDownloadService"
            android:exported="false"
            android:foregroundServiceType="dataSync">
            <intent-filter>
                <action android:name="androidx.media3.exoplayer.downloadService.action.RESTART" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </service>

        <receiver
            android:name="androidx.media3.session.MediaButtonReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MEDIA_BUTTON" />
            </intent-filter>
        </receiver>        

        <meta-data
            android:name="com.google.android.gms.car.application"
            android:resource="@xml/automotive_app_desc" />
    </application>

</manifest>
