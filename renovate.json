{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:recommended"], "ignoreUnstable": true, "respectLatest": true, "separateMajorMinor": true, "packageRules": [{"matchPackageNames": ["*"], "ignoreUnstable": true, "respectLatest": true}, {"matchManagers": ["gradle"], "enabled": false, "matchPackageNames": ["/^com\\.github\\.libre\\-tube:NewPipeExtractor$/"]}, {"description": "Ignore all pre-release versions (alpha, beta, rc, dev, snapshot)", "matchPackageNames": ["*"], "allowedVersions": "!/-(alpha|beta|rc|dev|snapshot|SNAPSHOT|Alpha|Beta|RC|Dev)/i"}]}