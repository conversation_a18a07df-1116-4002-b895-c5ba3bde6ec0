name: Build PR

on:
  pull_request:
    branches:
      - '**'

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v5
      - name: Set up Android SDK
        uses: android-actions/setup-android@v3
      - name: Install SDK components
        run: |
          sdkmanager --install "platform-tools" "platforms;android-36" "build-tools;36.0.0"
          yes | sdkmanager --licenses

      - name: Set up JDK 21
        uses: actions/setup-java@v5
        with:
          java-version: 21
          distribution: "temurin"

      - name: Set Up Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          cache-disabled: true
          cache-cleanup: on-success

      - name: Grant execute permission for gradlew
        run: chmod +x gradlew

      - name: Generate debug.keystore if not exists
        run: |
          mkdir -p ~/.android
          if [ ! -f ~/.android/debug.keystore ]; then
            keytool -genkey -v \
              -keystore ~/.android/debug.keystore \
              -storepass android \
              -alias androiddebugkey \
              -keypass android \
              -keyalg RSA \
              -keysize 2048 \
              -validity 10000 \
              -dname "CN=Android Debug,O=Android,C=US"
          fi

      - name: Build and Lint Universal Debug APK
        run: ./gradlew --no-configuration-cache --console=plain clean assembleUniversalDebug :app:lintUniversalDebug --warning-mode summary
        env:
           GITHUB_EVENT_NAME: ${{ github.event_name }}
           PULL_REQUEST: 'true'

      - name: Upload APK
        uses: actions/upload-artifact@v4
        with:
          name: app-universal-debug-pr-${{ github.event.number }}
          path: app/build/outputs/apk/universal/debug/*.apk
