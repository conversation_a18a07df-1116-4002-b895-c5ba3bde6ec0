package com.metrolist.music.ui.component

import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.LinearOutSlowInEasing
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import com.metrolist.music.constants.PlayerAnimations
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectVerticalDragGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.util.lerp
import androidx.compose.ui.zIndex
import kotlin.math.roundToInt

/**
 * Enhanced BottomSheet with YouTube Music-style smooth transitions
 * Features:
 * - Shared element transitions
 * - Smooth easing curves
 * - GPU-accelerated animations
 * - Parallax effects
 */
@Composable
fun EnhancedBottomSheet(
    state: BottomSheetState,
    modifier: Modifier = Modifier,
    backgroundColor: Color,
    onDismiss: (() -> Unit)? = null,
    collapsedContent: @Composable BoxScope.() -> Unit,
    content: @Composable BoxScope.() -> Unit,
) {
    val density = LocalDensity.current
    
    // Enhanced transition animations using YouTube Music-style easing
    val backgroundAlpha by animateFloatAsState(
        targetValue = when {
            state.isDismissed -> 0f
            state.isCollapsed -> 0.95f
            else -> 1f
        },
        animationSpec = if (state.isExpanded) {
            PlayerAnimations.BackgroundFadeAnimationSpec
        } else {
            PlayerAnimations.MiniPlayerFadeAnimationSpec
        },
        label = "background_alpha"
    )

    val cornerRadius by animateFloatAsState(
        targetValue = if (state.isExpanded) 0f else 16f,
        animationSpec = PlayerAnimations.SharedElementAnimationSpec,
        label = "corner_radius"
    )

    val elevation by animateFloatAsState(
        targetValue = if (state.isCollapsed) 8f else 0f,
        animationSpec = PlayerAnimations.MiniPlayerFadeAnimationSpec,
        label = "elevation"
    )

    Box(
        modifier = modifier
            .fillMaxSize()
            .offset {
                val y = (state.expandedBound - state.value)
                    .roundToPx()
                    .coerceAtLeast(0)
                IntOffset(x = 0, y = y)
            }
            .pointerInput(state) {
                detectVerticalDragGestures(
                    onVerticalDrag = { change, dragAmount ->
                        state.dispatchRawDelta(dragAmount)
                    },
                    onDragCancel = {
                        state.snapTo(state.collapsedBound)
                    },
                    onDragEnd = {
                        state.performFling(0f, onDismiss)
                    },
                )
            }
            .clip(
                RoundedCornerShape(
                    topStart = cornerRadius.dp,
                    topEnd = cornerRadius.dp,
                )
            )
            .graphicsLayer {
                // GPU acceleration for smooth animations
                this.alpha = backgroundAlpha
                this.shadowElevation = elevation
                // Enable hardware acceleration
                this.compositingStrategy = androidx.compose.ui.graphics.CompositingStrategy.Offscreen
            }
            .background(backgroundColor),
    ) {
        // Expanded content with enhanced fade-in
        if (!state.isCollapsed && !state.isDismissed) {
            BoxWithConstraints(
                modifier = Modifier
                    .fillMaxSize()
                    .graphicsLayer {
                        // Enhanced fade-in with parallax effect
                        val fadeProgress = ((state.progress - 0.2f) * 1.25f).coerceIn(0f, 1f)
                        alpha = fadeProgress
                        
                        // Subtle parallax effect for depth
                        translationY = (1f - fadeProgress) * 20f
                        
                        // Slight scale effect for smoothness
                        val scale = lerp(0.98f, 1f, fadeProgress)
                        scaleX = scale
                        scaleY = scale
                    }
                    .zIndex(1f), // Ensure expanded content is above collapsed content
                content = content,
            )
        }

        // Collapsed content with enhanced fade-out
        if (!state.isExpanded && (onDismiss == null || !state.isDismissed)) {
            Box(
                modifier = Modifier
                    .graphicsLayer {
                        // Enhanced fade-out with smooth transition
                        val fadeProgress = (1f - (state.progress * 2.5f)).coerceIn(0f, 1f)
                        alpha = fadeProgress
                        
                        // Subtle scale down effect
                        val scale = lerp(0.95f, 1f, fadeProgress)
                        scaleX = scale
                        scaleY = scale
                    }
                    .clickable(
                        interactionSource = remember { MutableInteractionSource() },
                        indication = null,
                        onClick = state::expandSoft,
                    )
                    .fillMaxWidth()
                    .height(state.collapsedBound)
                    .zIndex(0f), // Ensure collapsed content is below expanded content
                content = collapsedContent,
            )
        }
    }
}
