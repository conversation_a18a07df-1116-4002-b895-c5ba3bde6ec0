{"formatVersion": 1, "database": {"version": 21, "identityHash": "e0f58c96f6e849f8c8d09b75f9d4f3fe", "entities": [{"tableName": "song", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `title` TEXT NOT NULL, `duration` INTEGER NOT NULL, `thumbnailUrl` TEXT, `albumId` TEXT, `albumName` TEXT, `explicit` INTEGER NOT NULL DEFAULT 0, `year` INTEGER, `date` INTEGER, `dateModified` INTEGER, `liked` INTEGER NOT NULL, `likedDate` INTEGER, `totalPlayTime` INTEGER NOT NULL, `inLibrary` INTEGER, `dateDownload` INTEGER, `isLocal` INTEGER NOT NULL DEFAULT 0, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "duration", "columnName": "duration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "thumbnailUrl", "columnName": "thumbnailUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "albumId", "columnName": "albumId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "albumName", "columnName": "albumName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "explicit", "columnName": "explicit", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "year", "columnName": "year", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "date", "columnName": "date", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "dateModified", "columnName": "dateModified", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "liked", "columnName": "liked", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "likedDate", "columnName": "likedDate", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "totalPlayTime", "columnName": "totalPlayTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "inLibrary", "columnName": "inLibrary", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "dateDownload", "columnName": "dateDownload", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "isLocal", "columnName": "isLocal", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_song_albumId", "unique": false, "columnNames": ["albumId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_song_albumId` ON `${TABLE_NAME}` (`albumId`)"}], "foreignKeys": []}, {"tableName": "artist", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `thumbnailUrl` TEXT, `channelId` TEXT, `lastUpdateTime` INTEGER NOT NULL, `bookmarkedAt` INTEGER, `isLocal` INTEGER NOT NULL DEFAULT 0, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "thumbnailUrl", "columnName": "thumbnailUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "channelId", "columnName": "channelId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "lastUpdateTime", "columnName": "lastUpdateTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "bookmarkedAt", "columnName": "bookmarkedAt", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "isLocal", "columnName": "isLocal", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "album", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `playlistId` TEXT, `title` TEXT NOT NULL, `year` INTEGER, `thumbnailUrl` TEXT, `themeColor` INTEGER, `songCount` INTEGER NOT NULL, `duration` INTEGER NOT NULL, `explicit` INTEGER NOT NULL DEFAULT 0, `lastUpdateTime` INTEGER NOT NULL, `bookmarkedAt` INTEGER, `likedDate` INTEGER, `inLibrary` INTEGER, `isLocal` INTEGER NOT NULL DEFAULT 0, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "playlistId", "columnName": "playlistId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "year", "columnName": "year", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "thumbnailUrl", "columnName": "thumbnailUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "themeColor", "columnName": "themeColor", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "songCount", "columnName": "songCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "duration", "columnName": "duration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "explicit", "columnName": "explicit", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "lastUpdateTime", "columnName": "lastUpdateTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "bookmarkedAt", "columnName": "bookmarkedAt", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "likedDate", "columnName": "likedDate", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "inLibrary", "columnName": "inLibrary", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "isLocal", "columnName": "isLocal", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "playlist", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `browseId` TEXT, `createdAt` INTEGER, `lastUpdateTime` INTEGER, `isEditable` INTEGER NOT NULL DEFAULT true, `bookmarkedAt` INTEGER, `remoteSongCount` INTEGER, `playEndpointParams` TEXT, `shuffleEndpointParams` TEXT, `radioEndpointParams` TEXT, `isLocal` INTEGER NOT NULL DEFAULT 0, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "browseId", "columnName": "browseId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "lastUpdateTime", "columnName": "lastUpdateTime", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "isEditable", "columnName": "isEditable", "affinity": "INTEGER", "notNull": true, "defaultValue": "true"}, {"fieldPath": "bookmarkedAt", "columnName": "bookmarkedAt", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "remoteSongCount", "columnName": "remoteSongCount", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "playEndpointParams", "columnName": "playEndpointParams", "affinity": "TEXT", "notNull": false}, {"fieldPath": "shuffleEndpointParams", "columnName": "shuffleEndpointParams", "affinity": "TEXT", "notNull": false}, {"fieldPath": "radioEndpointParams", "columnName": "radioEndpointParams", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isLocal", "columnName": "isLocal", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "song_artist_map", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`songId` TEXT NOT NULL, `artistId` TEXT NOT NULL, `position` INTEGER NOT NULL, PRIMARY KEY(`songId`, `artistId`), FOREI<PERSON><PERSON>EY(`songId`) REFERENCES `song`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREI<PERSON><PERSON> KEY(`artistId`) REFERENCES `artist`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "songId", "columnName": "songId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "artistId", "columnName": "artistId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "position", "columnName": "position", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["songId", "artistId"]}, "indices": [{"name": "index_song_artist_map_songId", "unique": false, "columnNames": ["songId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_song_artist_map_songId` ON `${TABLE_NAME}` (`songId`)"}, {"name": "index_song_artist_map_artistId", "unique": false, "columnNames": ["artistId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_song_artist_map_artistId` ON `${TABLE_NAME}` (`artistId`)"}], "foreignKeys": [{"table": "song", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["songId"], "referencedColumns": ["id"]}, {"table": "artist", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["artistId"], "referencedColumns": ["id"]}]}, {"tableName": "song_album_map", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`songId` TEXT NOT NULL, `albumId` TEXT NOT NULL, `index` INTEGER NOT NULL, PRIMARY KEY(`songId`, `albumId`), FOREI<PERSON><PERSON>EY(`songId`) REFERENCES `song`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREI<PERSON><PERSON> KEY(`albumId`) REFERENCES `album`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "songId", "columnName": "songId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "albumId", "columnName": "albumId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "index", "columnName": "index", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["songId", "albumId"]}, "indices": [{"name": "index_song_album_map_songId", "unique": false, "columnNames": ["songId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_song_album_map_songId` ON `${TABLE_NAME}` (`songId`)"}, {"name": "index_song_album_map_albumId", "unique": false, "columnNames": ["albumId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_song_album_map_albumId` ON `${TABLE_NAME}` (`albumId`)"}], "foreignKeys": [{"table": "song", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["songId"], "referencedColumns": ["id"]}, {"table": "album", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["albumId"], "referencedColumns": ["id"]}]}, {"tableName": "album_artist_map", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`albumId` TEXT NOT NULL, `artistId` TEXT NOT NULL, `order` INTEGER NOT NULL, PRIMARY KEY(`albumId`, `artistId`), FOREI<PERSON><PERSON>EY(`albumId`) REFERENCES `album`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREI<PERSON><PERSON>EY(`artistId`) REFERENCES `artist`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "albumId", "columnName": "albumId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "artistId", "columnName": "artistId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "order", "columnName": "order", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["albumId", "artistId"]}, "indices": [{"name": "index_album_artist_map_albumId", "unique": false, "columnNames": ["albumId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_album_artist_map_albumId` ON `${TABLE_NAME}` (`albumId`)"}, {"name": "index_album_artist_map_artistId", "unique": false, "columnNames": ["artistId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_album_artist_map_artistId` ON `${TABLE_NAME}` (`artistId`)"}], "foreignKeys": [{"table": "album", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["albumId"], "referencedColumns": ["id"]}, {"table": "artist", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["artistId"], "referencedColumns": ["id"]}]}, {"tableName": "playlist_song_map", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `playlistId` TEXT NOT NULL, `songId` TEXT NOT NULL, `position` INTEGER NOT NULL, `setVideoId` TEXT, FOREIGN KEY(`playlistId`) REFERENCES `playlist`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREIGN KEY(`songId`) REFERENCES `song`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "playlistId", "columnName": "playlistId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "songId", "columnName": "songId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "position", "columnName": "position", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "setVideoId", "columnName": "setVideoId", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_playlist_song_map_playlistId", "unique": false, "columnNames": ["playlistId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_playlist_song_map_playlistId` ON `${TABLE_NAME}` (`playlistId`)"}, {"name": "index_playlist_song_map_songId", "unique": false, "columnNames": ["songId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_playlist_song_map_songId` ON `${TABLE_NAME}` (`songId`)"}], "foreignKeys": [{"table": "playlist", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["playlistId"], "referencedColumns": ["id"]}, {"table": "song", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["songId"], "referencedColumns": ["id"]}]}, {"tableName": "search_history", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `query` TEXT NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "query", "columnName": "query", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_search_history_query", "unique": true, "columnNames": ["query"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_search_history_query` ON `${TABLE_NAME}` (`query`)"}], "foreignKeys": []}, {"tableName": "format", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `itag` INTEGER NOT NULL, `mimeType` TEXT NOT NULL, `codecs` TEXT NOT NULL, `bitrate` INTEGER NOT NULL, `sampleRate` INTEGER, `contentLength` INTEGER NOT NULL, `loudnessDb` REAL, `playbackUrl` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "itag", "columnName": "itag", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "mimeType", "columnName": "mimeType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "codecs", "columnName": "codecs", "affinity": "TEXT", "notNull": true}, {"fieldPath": "bitrate", "columnName": "bitrate", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sampleRate", "columnName": "sampleRate", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "contentLength", "columnName": "contentLength", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "loudnessDb", "columnName": "loudnessDb", "affinity": "REAL", "notNull": false}, {"fieldPath": "playbackUrl", "columnName": "playbackUrl", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "lyrics", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `lyrics` TEXT NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "lyrics", "columnName": "lyrics", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "playCount", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`song` TEXT NOT NULL, `year` INTEGER NOT NULL, `month` INTEGER NOT NULL, `count` INTEGER NOT NULL, PRIMARY KEY(`song`, `year`, `month`))", "fields": [{"fieldPath": "song", "columnName": "song", "affinity": "TEXT", "notNull": true}, {"fieldPath": "year", "columnName": "year", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "month", "columnName": "month", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "count", "columnName": "count", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["song", "year", "month"]}, "indices": [], "foreignKeys": []}, {"tableName": "event", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `songId` TEXT NOT NULL, `timestamp` INTEGER NOT NULL, `playTime` INTEGER NOT NULL, FOREIGN KEY(`songId`) REFERENCES `song`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "songId", "columnName": "songId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "playTime", "columnName": "playTime", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_event_songId", "unique": false, "columnNames": ["songId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_event_songId` ON `${TABLE_NAME}` (`songId`)"}], "foreignKeys": [{"table": "song", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["songId"], "referencedColumns": ["id"]}]}, {"tableName": "related_song_map", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `songId` TEXT NOT NULL, `relatedSongId` TEXT NOT NULL, FOREI<PERSON><PERSON> KEY(`songId`) REFERENCES `song`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREIGN KEY(`relatedSongId`) REFERENCES `song`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "songId", "columnName": "songId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "relatedSongId", "columnName": "relatedSongId", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_related_song_map_songId", "unique": false, "columnNames": ["songId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_related_song_map_songId` ON `${TABLE_NAME}` (`songId`)"}, {"name": "index_related_song_map_relatedSongId", "unique": false, "columnNames": ["relatedSongId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_related_song_map_relatedSongId` ON `${TABLE_NAME}` (`relatedSongId`)"}], "foreignKeys": [{"table": "song", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["songId"], "referencedColumns": ["id"]}, {"table": "song", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["relatedSongId"], "referencedColumns": ["id"]}]}, {"tableName": "set_video_id", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`videoId` TEXT NOT NULL, `setVideoId` TEXT, PRIMARY KEY(`videoId`))", "fields": [{"fieldPath": "videoId", "columnName": "videoId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "setVideoId", "columnName": "setVideoId", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["videoId"]}, "indices": [], "foreignKeys": []}], "views": [{"viewName": "sorted_song_artist_map", "createSql": "CREATE VIEW `${VIEW_NAME}` AS SELECT * FROM song_artist_map ORDER BY position"}, {"viewName": "sorted_song_album_map", "createSql": "CREATE VIEW `${VIEW_NAME}` AS SELECT * FROM song_album_map ORDER BY `index`"}, {"viewName": "playlist_song_map_preview", "createSql": "CREATE VIEW `${VIEW_NAME}` AS SELECT * FROM playlist_song_map WHERE position <= 3 ORDER BY position"}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'e0f58c96f6e849f8c8d09b75f9d4f3fe')"]}}