# YouTube Music 共享元素过渡效果演示

## 🎯 现在的实现效果

### 真正的 YouTube Music 风格过渡
现在当你从 Mini Player 过渡到 Full Player 时，你会看到：

1. **独立的 Album Cover 动画** 🎵
   - Album Cover 从 Mini Player 的位置（左下角）
   - 沿着对角线轨迹"飞"到 Full Player 的中心位置
   - **不再跟随 BottomSheet 一起移动**

2. **平滑的形状变化** 🔄
   - 从小圆形 (48dp) 平滑缩放到大圆角矩形 (300dp)
   - 圆角半径从完全圆形逐渐变为圆角矩形
   - 整个过程无跳跃、无闪烁

3. **分层渲染** 📱
   - BottomSheet 在背景层从下往上滑动
   - Album Cover 在独立层进行轨迹动画
   - 两个动画同时进行但互不干扰

## 🔧 技术实现要点

### 核心架构改变
```
之前的实现：
┌─────────────────────────────────────┐
│ BottomSheet                        │
│  ├─ MiniPlayer (with album cover)  │ ← 整体一起移动
│  └─ FullPlayer (with album cover)  │
└─────────────────────────────────────┘

现在的实现：
┌─────────────────────────────────────┐
│ SharedAlbumCover (独立层)          │ ← 独立轨迹动画
├─────────────────────────────────────┤
│ BottomSheet                        │
│  ├─ MiniPlayer (无 album cover)    │ ← 背景滑动
│  └─ FullPlayer (无 album cover)    │
└─────────────────────────────────────┘
```

### 关键组件

1. **SharedAlbumCover.kt** - 新增
   - 独立渲染的 Album Cover
   - 精确的位置和大小计算
   - YouTube Music 风格的缓动曲线

2. **Player.kt** - 修改
   - 使用三层结构：背景 + BottomSheet + 共享元素
   - 传递正确的状态给共享元素

3. **MiniPlayer.kt** - 修改
   - 添加 `hideAlbumCover` 参数
   - 在共享元素模式下隐藏原有的 Album Cover

## 🎬 动画细节

### 位置动画
```kotlin
// 起始位置：Mini Player 左侧
miniPlayerX = 42.dp  // 考虑内边距的精确位置
miniPlayerY = screenHeight - miniPlayerHeight - 12.dp

// 结束位置：屏幕中央上方
fullPlayerX = screenWidth / 2f
fullPlayerY = screenHeight * 0.25f

// 平滑插值过渡
animatedX = miniPlayerX + (fullPlayerX - miniPlayerX) * progress
```

### 大小动画
```kotlin
// 48dp → 300dp 的平滑缩放
animatedSize = 48.dp + (300.dp - 48.dp) * progress
```

### 形状动画
```kotlin
// 圆形 → 圆角矩形
if (progress < 0.5f) {
    // 前半段：圆形逐渐变平
    cornerRadius = 24.dp * (1f - progress * 2f)
} else {
    // 后半段：变成圆角矩形
    cornerRadius = ThumbnailCornerRadius * 2
}
```

## 🚀 如何测试

### 1. 编译运行
```bash
./gradlew assembleDebug
```

### 2. 测试步骤
1. 播放任意音乐，确保 Mini Player 显示
2. **点击** Mini Player 或 **向上滑动**
3. 观察 Album Cover 的动画轨迹：
   - ✅ 应该从左下角对角线移动到中央
   - ✅ 大小平滑缩放
   - ✅ 形状平滑变化
   - ✅ 不跟随 BottomSheet 移动

### 3. 预期效果对比

**❌ 之前的效果**：
- Album Cover 跟随整个界面从下往上移动
- 轨迹是垂直的，不自然
- 没有真正的"飞行"效果

**✅ 现在的效果**：
- Album Cover 独立进行对角线轨迹动画
- 从 Mini Player 精确位置到 Full Player 精确位置
- 真正的 YouTube Music 风格共享元素过渡

## 🎨 视觉效果描述

想象一下：
1. 你看到底部的 Mini Player，左侧有一个小圆形的 Album Cover
2. 当你点击时，这个小圆形 Album Cover "脱离" Mini Player
3. 它沿着一条优美的对角线轨迹"飞"向屏幕中央
4. 在飞行过程中逐渐变大，从圆形变成圆角矩形
5. 同时，背景的 BottomSheet 从下往上滑动显示 Full Player 内容
6. 最终 Album Cover 精确落在 Full Player 的中心位置

这就是 YouTube Music 的经典过渡效果！

## 🔧 自定义调整

如果需要调整动画效果，可以修改：

### 动画时长
```kotlin
// 在 PlayerAnimations.kt 中
val SharedElementAnimationSpec = tween<Float>(
    durationMillis = 350, // 调整这里
    easing = CubicBezierEasing(0.4f, 0.0f, 0.2f, 1.0f)
)
```

### 轨迹位置
```kotlin
// 在 SharedAlbumCover.kt 中调整起始和结束位置
val fullPlayerY = (screenHeight * 0.25f).toPx() // 调整结束位置
```

### 大小比例
```kotlin
// 调整最终大小
val fullSize = 300.dp // 可以改为其他值
```

现在你拥有了真正的 YouTube Music 风格共享元素过渡动效！🎉
