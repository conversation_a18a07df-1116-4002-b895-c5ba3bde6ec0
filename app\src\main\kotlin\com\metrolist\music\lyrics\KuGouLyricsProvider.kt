package com.metrolist.music.lyrics

import android.content.Context
import com.metrolist.kugou.KuGou
import com.metrolist.music.constants.EnableKugouKey
import com.metrolist.music.utils.dataStore
import com.metrolist.music.utils.get

object KuGouLyricsProvider : LyricsProvider {
    override val name = "<PERSON><PERSON><PERSON>"
    override fun isEnabled(context: Context): Boolean =
        context.dataStore[EnableKugouKey] ?: true

    override suspend fun getLyrics(
        id: String,
        title: String,
        artist: String,
        duration: Int
    ): Result<String> =
        KuGou.getLyrics(title, artist, duration)

    override suspend fun getAllLyrics(
        id: String,
        title: String,
        artist: String,
        duration: Int,
        callback: (String) -> Unit
    ) {
        KuGou.getAllPossibleLyricsOptions(title, artist, duration, callback)
    }
}
