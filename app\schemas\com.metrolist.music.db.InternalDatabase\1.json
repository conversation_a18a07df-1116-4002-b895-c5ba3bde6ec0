{"formatVersion": 1, "database": {"version": 1, "identityHash": "38686a738e9e794eca8e1f635cf072b0", "entities": [{"tableName": "song", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `title` TEXT NOT NULL, `artistId` INTEGER NOT NULL, `duration` INTEGER NOT NULL, `liked` INTEGER NOT NULL, `artworkType` INTEGER NOT NULL, `isTrash` INTEGER NOT NULL, `download_state` INTEGER NOT NULL, `create_date` INTEGER NOT NULL, `modify_date` INTEGER NOT NULL, PRIMARY KEY(`id`), FOREIGN KEY(`artistId`) REFERENCES `artist`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "artistId", "columnName": "artistId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "duration", "columnName": "duration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "liked", "columnName": "liked", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "artworkType", "columnName": "artworkType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isTrash", "columnName": "isTrash", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadState", "columnName": "download_state", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createDate", "columnName": "create_date", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "modifyDate", "columnName": "modify_date", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": false}, "indices": [{"name": "index_song_id", "unique": true, "columnNames": ["id"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_song_id` ON `${TABLE_NAME}` (`id`)"}, {"name": "index_song_artistId", "unique": false, "columnNames": ["artistId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_song_artistId` ON `${TABLE_NAME}` (`artistId`)"}], "foreignKeys": [{"table": "artist", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["artistId"], "referencedColumns": ["id"]}]}, {"tableName": "artist", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `name` TEXT NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [{"name": "index_artist_id", "unique": true, "columnNames": ["id"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_artist_id` ON `${TABLE_NAME}` (`id`)"}], "foreignKeys": []}, {"tableName": "playlist", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`playlistId` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `name` TEXT NOT NULL)", "fields": [{"fieldPath": "playlistId", "columnName": "playlistId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}], "primaryKey": {"columnNames": ["playlistId"], "autoGenerate": true}, "indices": [{"name": "index_playlist_playlistId", "unique": true, "columnNames": ["playlistId"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_playlist_playlistId` ON `${TABLE_NAME}` (`playlistId`)"}], "foreignKeys": []}, {"tableName": "playlist_song", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `playlistId` INTEGER NOT NULL, `songId` TEXT NOT NULL, `idInPlaylist` INTEGER NOT NULL, FOREI<PERSON>N KEY(`playlistId`) REFERENCES `playlist`(`playlistId`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREI<PERSON><PERSON> KEY(`songId`) REFERENCES `song`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "playlistId", "columnName": "playlistId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "songId", "columnName": "songId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "idInPlaylist", "columnName": "idInPlaylist", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [{"name": "index_playlist_song_playlistId", "unique": false, "columnNames": ["playlistId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_playlist_song_playlistId` ON `${TABLE_NAME}` (`playlistId`)"}, {"name": "index_playlist_song_songId", "unique": false, "columnNames": ["songId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_playlist_song_songId` ON `${TABLE_NAME}` (`songId`)"}], "foreignKeys": [{"table": "playlist", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["playlistId"], "referencedColumns": ["playlistId"]}, {"table": "song", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["songId"], "referencedColumns": ["id"]}]}, {"tableName": "download", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `songId` TEXT NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "songId", "columnName": "songId", "affinity": "TEXT", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '38686a738e9e794eca8e1f635cf072b0')"]}}