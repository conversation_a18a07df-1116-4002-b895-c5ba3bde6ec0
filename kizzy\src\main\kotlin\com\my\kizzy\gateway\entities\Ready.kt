/*
 *
 *  ******************************************************************
 *  *  * Copyright (C) 2022
 *  *  * Ready.kt is part of Kizzy
 *  *  *  and can not be copied and/or distributed without the express
 *  *  * permission of <PERSON><PERSON><PERSON>(Vaibhav)
 *  *  *****************************************************************
 *
 *
 */

package com.my.kizzy.gateway.entities


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Ready(
    @SerialName("resume_gateway_url")
    val resumeGatewayUrl: String? = null,
    @SerialName("session_id")
    val sessionId: String? = null,
)